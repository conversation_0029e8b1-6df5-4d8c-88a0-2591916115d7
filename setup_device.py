#!/usr/bin/env python3
"""
Script to set up device configuration for HacknDroid
"""

import sys
import os
import configparser
sys.path.append('.')

from modules.adb import adb_devices_list
from termcolor import colored

def setup_device():
    """Set up device configuration automatically"""
    
    print(colored("=== HacknDroid Device Setup ===", 'cyan'))
    print()
    
    try:
        # Get list of connected devices
        devices = adb_devices_list()
        
        if not devices:
            print(colored("No devices found. Please connect your Android device and enable USB debugging.", 'red'))
            return False
        
        print(colored(f"Found {len(devices)} device(s):", 'green'))
        for i, device in enumerate(devices):
            device_id, device_name, status, model = device
            print(colored(f"  {i}: {device_name} ({model}) - {status}", 'yellow'))
        
        # If only one device, select it automatically
        if len(devices) == 1:
            selected_device = devices[0]
            print(colored(f"Automatically selecting the only device: {selected_device[1]}", 'green'))
        else:
            # Let user choose
            while True:
                try:
                    choice = int(input(colored("Select device number: ", 'green')))
                    if 0 <= choice < len(devices):
                        selected_device = devices[choice]
                        break
                    else:
                        print(colored("Invalid choice. Please try again.", 'red'))
                except ValueError:
                    print(colored("Please enter a valid number.", 'red'))
        
        # Configure the device in config.ini
        device_id, device_name, status, model = selected_device
        
        config = configparser.ConfigParser()
        script_folder = os.path.dirname(os.path.abspath(__file__))
        config_file_path = os.path.join(script_folder, "config.ini")
        
        if os.path.exists(config_file_path):
            config.read(config_file_path)
        
        # Add or update the General section
        if not config.has_section('General'):
            config.add_section('General')
        
        config.set('General', 'adb_session_device', device_name)
        config.set('General', 'adb_session_model', model)
        
        # Write the configuration to file
        with open(config_file_path, 'w') as configfile:
            config.write(configfile)
        
        print(colored(f"✓ Device configured successfully!", 'green'))
        print(colored(f"  Device ID: {device_name}", 'green'))
        print(colored(f"  Model: {model}", 'green'))
        print(colored(f"  Status: {status}", 'green'))
        
        return True
        
    except Exception as e:
        print(colored(f"Error setting up device: {str(e)}", 'red'))
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = setup_device()
    
    if success:
        print()
        print(colored("Device setup complete! You can now use HacknDroid APK extraction features.", 'green'))
        print(colored("Try running the test again: python test_apk_extraction.py", 'cyan'))
    else:
        print()
        print(colored("Device setup failed. Please check your ADB connection and try again.", 'red'))
