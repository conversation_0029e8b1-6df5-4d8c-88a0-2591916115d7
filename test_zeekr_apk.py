#!/usr/bin/env python3
"""
Test script to verify APK extraction for com.zeekr.applab
"""

import sys
import os
sys.path.append('.')

from modules.apk_analyzer import get_apk_from_device
from termcolor import colored

def test_zeekr_apk():
    """Test APK extraction for com.zeekr.applab"""
    
    print(colored("=== Testing Zeekr AppLab APK Extraction ===", 'cyan'))
    print()
    
    app_id = "com.zeekr.applab"
    
    try:
        print(colored(f"Testing APK extraction for: {app_id}", 'yellow'))
        
        apk_filepath, extracted_app_id = get_apk_from_device(app_id, False)
        
        if apk_filepath and extracted_app_id:
            print(colored(f"✓ Successfully extracted APK: {apk_filepath}", 'green'))
            print(colored(f"  App ID: {extracted_app_id}", 'green'))
            
            if os.path.exists(apk_filepath):
                file_size = os.path.getsize(apk_filepath)
                print(colored(f"  File size: {file_size} bytes", 'green'))
                print(colored(f"  File exists: ✓", 'green'))
                return True
            else:
                print(colored(f"  Warning: APK file doesn't exist at {apk_filepath}", 'yellow'))
                return False
        else:
            print(colored(f"✗ Failed to extract APK for {app_id}", 'red'))
            return False
            
    except Exception as e:
        print(colored(f"✗ Exception while testing {app_id}: {str(e)}", 'red'))
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_zeekr_apk()
    
    print()
    if success:
        print(colored("✓ Zeekr AppLab APK extraction successful!", 'green'))
    else:
        print(colored("✗ Zeekr AppLab APK extraction failed.", 'red'))
