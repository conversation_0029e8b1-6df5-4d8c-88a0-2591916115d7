#!/usr/bin/env python3
"""
Test script to diagnose APK extraction issues in HacknDroid
"""

import sys
import os
sys.path.append('.')

from modules.apk_analyzer import get_apk_from_device
from modules.utility import app_id_from_user_input
from termcolor import colored

def test_apk_extraction():
    """Test APK extraction with a known app"""
    
    print(colored("=== HacknDroid APK Extraction Test ===", 'cyan'))
    print()
    
    # Test with a common Android app
    test_apps = [
        "com.android.calculator2",  # Calculator
        "com.android.gallery3d",    # Gallery
        "com.android.launcher3",    # Launcher
        "com.android.settings"      # Settings
    ]
    
    for app_id in test_apps:
        print(colored(f"Testing with app: {app_id}", 'yellow'))
        try:
            apk_filepath, extracted_app_id = get_apk_from_device(app_id, False)
            
            if apk_filepath and extracted_app_id:
                print(colored(f"✓ Successfully extracted APK: {apk_filepath}", 'green'))
                print(colored(f"  App ID: {extracted_app_id}", 'green'))
                if os.path.exists(apk_filepath):
                    file_size = os.path.getsize(apk_filepath)
                    print(colored(f"  File size: {file_size} bytes", 'green'))
                else:
                    print(colored(f"  Warning: APK file doesn't exist at {apk_filepath}", 'yellow'))
                print()
                return True  # Success with at least one app
            else:
                print(colored(f"✗ Failed to extract APK for {app_id}", 'red'))
                
        except Exception as e:
            print(colored(f"✗ Exception while testing {app_id}: {str(e)}", 'red'))
        
        print()
    
    print(colored("All test apps failed. This suggests an issue with device connection or permissions.", 'red'))
    return False

def test_app_id_resolution():
    """Test app ID resolution"""
    print(colored("=== Testing App ID Resolution ===", 'cyan'))
    
    try:
        # Test with a keyword search
        print(colored("Testing keyword search with 'calculator'...", 'yellow'))
        # This would normally prompt for user input, so we'll skip it for automated testing
        print(colored("Skipping interactive test (would require user input)", 'yellow'))
        
    except Exception as e:
        print(colored(f"Error in app ID resolution test: {str(e)}", 'red'))

if __name__ == "__main__":
    print(colored("Starting HacknDroid APK extraction diagnostics...", 'cyan'))
    print()
    
    # Test basic APK extraction
    success = test_apk_extraction()
    
    # Test app ID resolution
    test_app_id_resolution()
    
    print()
    if success:
        print(colored("✓ At least one APK extraction succeeded!", 'green'))
        print(colored("The improved error handling should now provide better diagnostics.", 'green'))
    else:
        print(colored("✗ All APK extractions failed.", 'red'))
        print(colored("Possible issues:", 'yellow'))
        print(colored("1. Device not properly connected via ADB", 'yellow'))
        print(colored("2. Device doesn't have the test apps installed", 'yellow'))
        print(colored("3. ADB permissions issue", 'yellow'))
        print(colored("4. Device storage access restrictions", 'yellow'))
