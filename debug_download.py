#!/usr/bin/env python3
"""
Debug script to test the download function
"""

import sys
import os
sys.path.append('.')

from modules.file_transfer import download
from modules.utility import current_date
from termcolor import colored

def test_download():
    """Test the download function directly"""
    
    print(colored("=== Testing Download Function ===", 'cyan'))
    
    # Test downloading the calculator APK
    source = "/system/app/ExactCalculator/ExactCalculator.apk"
    
    # Create destination path
    now = current_date()
    dest_folder = os.path.join("results", "debug_test", now)
    os.makedirs(dest_folder, exist_ok=True)
    destination = os.path.join(dest_folder, "ExactCalculator.apk")
    
    print(colored(f"Source: {source}", 'yellow'))
    print(colored(f"Destination: {destination}", 'yellow'))
    print()
    
    # Test the download
    success = download(source, destination)
    
    print()
    print(colored(f"Download result: {success}", 'cyan'))
    
    if os.path.exists(destination):
        file_size = os.path.getsize(destination)
        print(colored(f"File exists: {destination}", 'green'))
        print(colored(f"File size: {file_size} bytes", 'green'))
    else:
        print(colored(f"File does not exist: {destination}", 'red'))

if __name__ == "__main__":
    test_download()
